import { Component, Input, OnInit } from '@angular/core';
import { AccountService } from 'src/app/modules/account/services/account.service';
import { ModalService } from 'src/app/shared/services/modal.service';
import { forkJoin } from 'rxjs';
import { EMPTY_GUID } from '../../constant';

@Component({
    selector: 'app-service-activities',
    templateUrl: './service-activities.component.html',
    styleUrls: ['./service-activities.component.scss']
})
export class ServiceActivitiesComponent implements OnInit {
    @Input() userId: string = '';
    @Input() userProfilePhoto: string = '';
    @Input() userName: string = '';
    @Input() userRole: string = '';
    @Input() isLoggedIn: boolean = false;
    @Input() currentUserId: string = '';
    @Input() currentUserPhoto: string = '';
    @Input() currentUserName: string = '';

    serviceActivities: any[] = [];
    serviceActivitiesLoading = false;
    commentInputs: { [activityId: string]: string } = {};
    showAllComments: { [activityId: string]: boolean } = {};

    constructor(
        private accountService: AccountService,
        private modalService: ModalService
    ) { }

    ngOnInit(): void {
        console.log('ServiceActivitiesComponent initialized with:', {
            userId: this.userId,
            userProfilePhoto: this.userProfilePhoto,
            userName: this.userName,
            userRole: this.userRole,
            isLoggedIn: this.isLoggedIn,
            currentUserId: this.currentUserId,
            currentUserPhoto: this.currentUserPhoto,
            currentUserName: this.currentUserName
        });

        if (this.userId) {
            this.getServiceActivities();
        }
    }

    getServiceActivities() {
        this.serviceActivitiesLoading = true;
        this.accountService.getServiceActivities(this.userId).subscribe({
            next: (response: any) => {
                // Sort by createdAt descending (latest first)
                this.serviceActivities = (response || []).sort((a: any, b: any) =>
                    new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime()
                );

                // Initialize default like properties for each activity (but don't reset existing values)
                this.serviceActivities.forEach((activity: any) => {
                    // Only set defaults if the properties are completely undefined, don't reset to 0
                    if (activity.likeCount === undefined || activity.likeCount === null) activity.likeCount = 0;
                    if (activity.isLiked === undefined || activity.isLiked === null) activity.isLiked = false;
                    if (activity.likeId === undefined) activity.likeId = null;
                });

                console.log('Service activities response:', response);
                if (this.serviceActivities.length > 0) {
                    console.log('First activity structure:', this.serviceActivities[0]);
                }
                // Load comments and likes for each activity
                this.loadCommentsAndLikesForActivities();
            },
            error: (err) => {
                console.error('Service activities error:', err);
                this.serviceActivities = [];
                this.serviceActivitiesLoading = false;
            }
        });
    }

    loadCommentsAndLikesForActivities() {
        if (this.serviceActivities.length === 0) {
            this.serviceActivitiesLoading = false;
            return;
        }

        console.log('Loading comments and likes for activities. Current user ID:', this.currentUserId);

        // Create an array of comment requests and like count requests for each activity
        const commentRequests = this.serviceActivities.map(activity =>
            this.accountService.getActivityComments(activity.id)
        );

        const likeRequests = this.serviceActivities.map(activity =>
            this.accountService.getPostLikes(activity.id)
        );

        // Combine both requests
        forkJoin([
            forkJoin(commentRequests),
            forkJoin(likeRequests)
        ]).subscribe({
            next: ([commentsResponses, likesResponses]: [any[], any[]]) => {
                console.log('Comments loaded for activities:', commentsResponses);
                console.log('Likes loaded for activities:', likesResponses);

                // Assign comments and likes to each activity
                this.serviceActivities.forEach((activity, index) => {
                    activity.comments = commentsResponses[index] || [];

                    // Handle like count response
                    const likeResponse = likesResponses[index];
                    // console.log(`Activity ${index} like response:`, likeResponse);

                    if (Array.isArray(likeResponse)) {
                        // Handle case where API returns array of likes (most common format)
                        activity.likeCount = likeResponse.length;

                        // Only check for user's like status if currentUserId is available
                        if (this.currentUserId && this.currentUserId.trim() !== '') {
                            activity.isLiked = likeResponse.some((like: any) =>
                                like.userId === this.currentUserId || like.user?.id === this.currentUserId
                            );

                            // Find the current user's like ID if they liked the post
                            const userLike = likeResponse.find((like: any) =>
                                like.userId === this.currentUserId || like.user?.id === this.currentUserId
                            );
                            activity.likeId = userLike ? (userLike.id || userLike.likeId) : null;
                        } else {
                            // If no currentUserId, assume not liked but keep the count
                            activity.isLiked = false;
                            activity.likeId = null;
                        }

                        console.log(`Activity ${index} array like data:`, {
                            likeCount: activity.likeCount,
                            isLiked: activity.isLiked,
                            likeId: activity.likeId,
                            currentUserId: this.currentUserId,
                            likesArray: likeResponse
                        });
                    } else if (likeResponse && typeof likeResponse === 'object') {
                        // Log the full response structure to understand the API format
                        console.log(`Activity ${index} FULL object response:`, likeResponse);
                        console.log(`Activity ${index} response keys:`, Object.keys(likeResponse));
                        console.log(`Activity ${index} response values:`, Object.values(likeResponse));

                        // Try different possible response formats for object response
                        const likeCount = likeResponse.likeCount || likeResponse.count || likeResponse.totalLikes || likeResponse.data?.length || likeResponse.length;
                        const isLiked = likeResponse.isLiked || likeResponse.likedByCurrentUser || likeResponse.userLiked;
                        const likeId = likeResponse.likeId || likeResponse.id;

                        console.log('Parsed object response data:', { likeCount, isLiked, likeId });

                        // Check if the response has a 'data' property that contains the actual likes array
                        if (likeResponse.data && Array.isArray(likeResponse.data)) {
                            console.log(`Activity ${index} found data array in object response:`, likeResponse.data);
                            // Process as array within object
                            activity.likeCount = likeResponse.data.length;

                            if (this.currentUserId && this.currentUserId.trim() !== '') {
                                activity.isLiked = likeResponse.data.some((like: any) =>
                                    like.userId === this.currentUserId || like.user?.id === this.currentUserId
                                );

                                const userLike = likeResponse.data.find((like: any) =>
                                    like.userId === this.currentUserId || like.user?.id === this.currentUserId
                                );
                                activity.likeId = userLike ? (userLike.id || userLike.likeId) : null;
                            } else {
                                activity.isLiked = false;
                                activity.likeId = null;
                            }
                        } else {
                            // Always set the like count if available
                            activity.likeCount = likeCount !== undefined && likeCount !== null ? likeCount : 0;

                            // Only set user-specific like status if currentUserId is available
                            if (this.currentUserId && this.currentUserId.trim() !== '') {
                                activity.isLiked = isLiked !== undefined && isLiked !== null ? isLiked : false;
                                activity.likeId = (activity.isLiked && likeId) ? likeId : null;
                            } else {
                                activity.isLiked = false;
                                activity.likeId = null;
                            }
                        }

                        console.log(`Activity ${index} final object like data:`, {
                            likeCount: activity.likeCount,
                            isLiked: activity.isLiked,
                            likeId: activity.likeId,
                            currentUserId: this.currentUserId,
                            originalResponse: likeResponse
                        });
                    } else {
                        // Default values if no valid response
                        activity.likeCount = 0;
                        activity.isLiked = false;
                        activity.likeId = null;
                        console.log(`Activity ${index} no like data, using defaults`);
                    }

                    // Enhance comments with current user info if missing
                    activity.comments.forEach((comment: any) => {
                        if (comment.userId === this.currentUserId) {
                            console.log('Initial load - Enhancing comment for current user:', {
                                commentId: comment.id,
                                originalPhoto: comment.userProfilePhoto,
                                currentUserPhoto: this.currentUserPhoto,
                                originalName: comment.userName,
                                currentUserName: this.currentUserName
                            });

                            // If this comment is from the current user and missing profile info, add it
                            if (!comment.userProfilePhoto && this.currentUserPhoto) {
                                comment.userProfilePhoto = this.currentUserPhoto;
                                console.log('Initial load - Updated comment photo to:', comment.userProfilePhoto);
                            }
                            if (!comment.userName && this.currentUserName) {
                                comment.userName = this.currentUserName;
                                console.log('Initial load - Updated comment name to:', comment.userName);
                            }
                        }
                    });

                    // Sort comments by createdAt in descending order (newest first)
                    activity.comments.sort((a: any, b: any) =>
                        new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime()
                    );
                    console.log(`Activity ${activity.id} has ${activity.comments.length} comments`);
                });
                this.serviceActivitiesLoading = false;
            },
            error: (err) => {
                console.error('Error loading comments and likes:', err);
                // Initialize empty comments arrays and default like values if loading fails
                this.serviceActivities.forEach((activity: any) => {
                    if (!activity.comments) activity.comments = [];
                    if (activity.likeCount === undefined) activity.likeCount = 0;
                    if (activity.isLiked === undefined) activity.isLiked = false;
                    if (activity.likeId === undefined) activity.likeId = null;
                });
                this.serviceActivitiesLoading = false;
            }
        });
    }
    // ...existing code...

    toggleLike(activity: any) {
        if (!this.isLoggedIn || !this.currentUserId) return;

        // console.log('Activity object for like:', activity);
        // console.log('Available activity fields:', Object.keys(activity));
        // console.log('Current like state:', { isLiked: activity.isLiked, likeCount: activity.likeCount, likeId: activity.likeId });

        // Store original state for rollback if needed
        const originalIsLiked = activity.isLiked;
        const originalLikeCount = activity.likeCount;
        const originalLikeId = activity.likeId;

        // Immediately update UI for better user experience
        activity.isLiked = !activity.isLiked;
        if (activity.isLiked) {
            activity.likeCount = (activity.likeCount || 0) + 1;
        } else {
            activity.likeCount = Math.max((activity.likeCount || 1) - 1, 0);
            activity.likeId = null;
        }

        // Determine the correct ID based on like/unlike action (using original state)
        let likeId: string;
        if (originalIsLiked && originalLikeId) {
            // User is unliking - use the actual like ID
            likeId = originalLikeId;
        } else {
            // User is liking - use empty GUID
            likeId = EMPTY_GUID;
        }

        const payload = {
            id: likeId,
            userId: this.currentUserId,
            userPostId: activity.id // activity.id is the post id
        };

        // console.log('Sending like payload:', payload);

        this.accountService.addOrRemovePostLike(payload).subscribe({
            next: (res: any) => {
                console.log('Like response:', res);

                // Check if the response indicates success
                if (res && res.messageType === 0) {
                    // Success - update likeId if we got one back
                    if (activity.isLiked && res && res.data) {
                        activity.likeId = res.data;
                    }
                    // Don't call refreshLikeCount immediately - let the optimistic update stand
                    // The like count will be refreshed on next page load or manual refresh
                    console.log('Like action successful, keeping optimistic update');
                } else {
                    // If not successful, rollback to original state
                    console.log('Like action failed, rolling back');
                    activity.isLiked = originalIsLiked;
                    activity.likeCount = originalLikeCount;
                    activity.likeId = originalLikeId;
                }
            },
            error: (err) => {
                console.error('Error toggling like:', err);
                console.error('Error details:', err.error);

                // Rollback to original state on error
                activity.isLiked = originalIsLiked;
                activity.likeCount = originalLikeCount;
                activity.likeId = originalLikeId;
            }
        });
    }

    refreshLikeCount(activity: any) {
        // Store current state before API call
        const currentIsLiked = activity.isLiked;
        const currentLikeCount = activity.likeCount;
        const currentLikeId = activity.likeId;

        this.accountService.getPostLikes(activity.id).subscribe({
            next: (response: any) => {
                console.log('Refreshed like count response:', response);
                console.log('Response type:', typeof response);
                console.log('Response keys:', response ? Object.keys(response) : 'null');

                // Check if response has valid data
                if (response && typeof response === 'object') {
                    if (Array.isArray(response)) {
                        // Handle case where API returns array of likes (same as initial load)
                        activity.likeCount = response.length;

                        // Only check for user's like status if currentUserId is available
                        if (this.currentUserId && this.currentUserId.trim() !== '') {
                            activity.isLiked = response.some((like: any) =>
                                like.userId === this.currentUserId || like.user?.id === this.currentUserId
                            );

                            // Find the current user's like ID if they liked the post
                            const userLike = response.find((like: any) =>
                                like.userId === this.currentUserId || like.user?.id === this.currentUserId
                            );
                            activity.likeId = userLike ? (userLike.id || userLike.likeId) : null;
                        } else {
                            // If no currentUserId, preserve current state or set to false
                            activity.isLiked = currentIsLiked;
                            activity.likeId = currentLikeId;
                        }

                        console.log('Updated activity with array response:', {
                            likeCount: activity.likeCount,
                            isLiked: activity.isLiked,
                            likeId: activity.likeId,
                            currentUserId: this.currentUserId,
                            likesArray: response
                        });
                    } else {
                        // Log the full response structure to understand the API format
                        console.log('REFRESH - FULL object response:', response);
                        console.log('REFRESH - response keys:', Object.keys(response));
                        console.log('REFRESH - response values:', Object.values(response));

                        // Try different possible response formats for object response
                        const likeCount = response.likeCount || response.count || response.totalLikes || response.data?.length || response.length;
                        const isLiked = response.isLiked || response.likedByCurrentUser || response.userLiked;
                        const likeId = response.likeId || response.id;

                        console.log('REFRESH - Parsed object response data:', { likeCount, isLiked, likeId });

                        // Check if the response has a 'data' property that contains the actual likes array
                        if (response.data && Array.isArray(response.data)) {
                            console.log('REFRESH - found data array in object response:', response.data);
                            // Process as array within object
                            activity.likeCount = response.data.length;

                            if (this.currentUserId && this.currentUserId.trim() !== '') {
                                activity.isLiked = response.data.some((like: any) =>
                                    like.userId === this.currentUserId || like.user?.id === this.currentUserId
                                );

                                const userLike = response.data.find((like: any) =>
                                    like.userId === this.currentUserId || like.user?.id === this.currentUserId
                                );
                                activity.likeId = userLike ? (userLike.id || userLike.likeId) : null;
                            } else {
                                activity.isLiked = currentIsLiked;
                                activity.likeId = currentLikeId;
                            }
                        } else {
                            // Only update if we got meaningful data, otherwise keep current state
                            if (likeCount !== undefined && likeCount !== null) {
                                activity.likeCount = likeCount;
                            } else {
                                activity.likeCount = currentLikeCount;
                            }

                            if (isLiked !== undefined && isLiked !== null) {
                                activity.isLiked = isLiked;
                                activity.likeId = isLiked ? (likeId || currentLikeId) : null;
                            } else {
                                activity.isLiked = currentIsLiked;
                                activity.likeId = currentLikeId;
                            }
                        }

                        console.log('Updated activity with object response:', {
                            likeCount: activity.likeCount,
                            isLiked: activity.isLiked,
                            likeId: activity.likeId
                        });
                    }
                } else {
                    // Keep current state if response is not valid
                    console.log('Invalid API response, keeping current state');
                    activity.likeCount = currentLikeCount;
                    activity.isLiked = currentIsLiked;
                    activity.likeId = currentLikeId;
                }
            },
            error: (err) => {
                console.error('Error refreshing like count:', err);
                // Keep current state on error
                console.log('API error, keeping current state');
                activity.likeCount = currentLikeCount;
                activity.isLiked = currentIsLiked;
                activity.likeId = currentLikeId;
            }
        });
    }



    postComment(activity: any) {
        if (!this.isLoggedIn || !this.currentUserId || !this.commentInputs[activity.id]?.trim()) {
            console.log('Cannot post comment:', {
                isLoggedIn: this.isLoggedIn,
                currentUserId: this.currentUserId,
                commentText: this.commentInputs[activity.id]
            });
            return;
        }

        const payload = {
            activityId: activity.id,
            userId: this.currentUserId,
            content: this.commentInputs[activity.id]
        };

        console.log('Posting comment with payload:', payload);
        console.log('Current user info for comment:', {
            currentUserName: this.currentUserName,
            currentUserPhoto: this.currentUserPhoto,
            userName: this.userName,
            userProfilePhoto: this.userProfilePhoto
        });

        // Store comment text before clearing
        const commentText = this.commentInputs[activity.id];

        this.accountService.addActivityComment(payload).subscribe({
            next: (res: any) => {
                console.log('Comment posted successfully:', res);
                // Reload comments for this activity to get the latest data from server
                this.accountService.getActivityComments(activity.id).subscribe({
                    next: (commentsResponse: any[]) => {
                        console.log('Comments reloaded:', commentsResponse);
                        activity.comments = commentsResponse || [];

                        // Enhance comments with current user info if missing
                        activity.comments.forEach((comment: any) => {
                            if (comment.userId === this.currentUserId) {
                                console.log('Enhancing comment for current user:', {
                                    commentId: comment.id,
                                    originalPhoto: comment.userProfilePhoto,
                                    currentUserPhoto: this.currentUserPhoto,
                                    originalName: comment.userName,
                                    currentUserName: this.currentUserName
                                });

                                // If this comment is from the current user and missing profile info, add it
                                if (!comment.userProfilePhoto && this.currentUserPhoto) {
                                    comment.userProfilePhoto = this.currentUserPhoto;
                                    console.log('Updated comment photo to:', comment.userProfilePhoto);
                                }
                                if (!comment.userName && this.currentUserName) {
                                    comment.userName = this.currentUserName;
                                    console.log('Updated comment name to:', comment.userName);
                                }
                            }
                        });

                        // Sort comments by createdAt in descending order (newest first)
                        activity.comments.sort((a: any, b: any) =>
                            new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime()
                        );
                        this.commentInputs[activity.id] = '';
                    },
                    error: (err) => {
                        console.error('Error reloading comments:', err);
                        // Fallback: add comment locally if reload fails
                        const newComment = {
                            userId: this.currentUserId,
                            content: commentText,
                            createdAt: new Date(),
                            userName: this.currentUserName || this.userName || 'User',
                            userProfilePhoto: this.currentUserPhoto || this.userProfilePhoto || './assets/images/default-avatar.png'
                        };
                        console.log('Adding fallback comment:', newComment);
                        activity.comments = activity.comments || [];
                        activity.comments.unshift(newComment); // Add to beginning since we sort newest first
                        this.commentInputs[activity.id] = '';
                    }
                });
            },
            error: (err) => {
                console.error('Error posting comment:', err);
                // Also add fallback comment if the post itself fails
                const newComment = {
                    userId: this.currentUserId,
                    content: commentText,
                    createdAt: new Date(),
                    userName: this.currentUserName || this.userName || 'User',
                    userProfilePhoto: this.currentUserPhoto || this.userProfilePhoto || './assets/images/default-avatar.png'
                };
                console.log('Adding fallback comment after post error:', newComment);
                activity.comments = activity.comments || [];
                activity.comments.unshift(newComment); // Add to beginning since we sort newest first
                this.commentInputs[activity.id] = '';
            }
        });
    }

    onCommentKeyDown(event: KeyboardEvent, activity: any) {
        if (event.key === 'Enter' && this.commentInputs[activity.id]?.trim()) {
            this.postComment(activity);
        }
    }

    createPost() {
        console.log('Create post clicked', { isLoggedIn: this.isLoggedIn, currentUserId: this.currentUserId });

        if (!this.isLoggedIn || !this.currentUserId) {
            console.log('User not logged in or no current user ID');
            return;
        }

        const modalData = {
            initialState: {
                isEditMode: false,
                postData: null,
                userId: this.currentUserId,
                userProfilePhoto: this.userProfilePhoto,
                userName: this.userName
            }
        };

        console.log('Opening create post modal with data:', modalData);
        const modalRef = this.modalService.openModal('create-edit-post', modalData);
        console.log('Modal ref:', modalRef);

        if (modalRef) {
            modalRef.content?.onClose?.subscribe((result: any) => {
                console.log('Modal closed with result:', result);
                if (result && result.success) {
                    this.getServiceActivities();
                }
            });
        }
    }

    editPost(activity: any) {
        console.log('Edit post clicked', { activity, isLoggedIn: this.isLoggedIn, currentUserId: this.currentUserId });

        // Use the same logic as canEditPost for authorization
        if (!this.canEditPost(activity)) {
            console.log('User not authorized to edit this post');
            return;
        }

        const modalData = {
            initialState: {
                isEditMode: true,
                postData: activity,
                userId: this.currentUserId,
                userProfilePhoto: this.userProfilePhoto,
                userName: this.userName
            }
        };

        console.log('Opening edit post modal with data:', modalData);
        const modalRef = this.modalService.openModal('create-edit-post', modalData);
        console.log('Modal ref:', modalRef);

        if (modalRef) {
            modalRef.content?.onClose?.subscribe((result: any) => {
                console.log('Modal closed with result:', result);
                if (result && result.success) {
                    this.getServiceActivities();
                }
            });
        }
    }

    canEditPost(activity: any): boolean {
        console.log('canEditPost check:', {
            isLoggedIn: this.isLoggedIn,
            currentUserId: this.currentUserId,
            activityUserId: activity.userId,
            profileUserId: this.userId,
            activity: activity
        });

        // Check if activity has userId field
        if (activity.userId) {
            return !!(this.isLoggedIn && this.currentUserId &&
                activity.userId.toLowerCase() === this.currentUserId.toLowerCase());
        }

        // Fallback: If we're viewing the user's own profile and they're logged in,
        // assume they own all posts on their profile
        const isOwnProfile = !!(this.isLoggedIn && this.currentUserId && this.userId &&
            this.userId.toLowerCase() === this.currentUserId.toLowerCase());

        // console.log('Fallback ownership check:', {
        //     isOwnProfile,
        //     explanation: 'Using profile ownership since activity.userId is missing'
        // });

        return isOwnProfile;
    }

    isOwnProfile(): boolean {
        const isOwn = !!(this.isLoggedIn && this.currentUserId && this.userId &&
            this.userId.toLowerCase() === this.currentUserId.toLowerCase());
        // console.log('isOwnProfile check:', {
        //     isLoggedIn: this.isLoggedIn,
        //     currentUserId: this.currentUserId,
        //     profileUserId: this.userId,
        //     isOwnProfile: isOwn
        // });
        return isOwn;
    }

    getDisplayName(activity: any): string {
        // Try activity data first
        if (activity.userName && activity.userName.trim()) {
            return activity.userName;
        }

        // Try firstName + lastName from activity
        if (activity.firstName || activity.lastName) {
            const firstName = activity.firstName || '';
            const lastName = activity.lastName || '';
            const fullName = `${firstName} ${lastName}`.trim();
            if (fullName) {
                return fullName;
            }
        }

        // Fallback to input parameters (profile data)
        return this.userName || 'User';
    }

    getDisplayRole(activity: any): string {
        // Try activity data first
        if (activity.userRole && activity.userRole.trim()) {
            return activity.userRole;
        }

        if (activity.roleName && activity.roleName.trim()) {
            return activity.roleName;
        }

        // Fallback to input parameters (profile data)
        return this.userRole || '';
    }

    getDisplayPhoto(activity: any): string {
        // Try activity data first
        if (activity.userProfilePhoto && activity.userProfilePhoto.trim()) {
            return activity.userProfilePhoto;
        }

        if (activity.profilePhoto && activity.profilePhoto.trim()) {
            return activity.profilePhoto;
        }

        // Fallback to input parameters (profile data)
        return this.userProfilePhoto || './assets/images/default-avatar.png';
    }

    getCommentDisplayName(comment: any): string {
        // Try comment data first
        if (comment.userName && comment.userName.trim()) {
            return comment.userName;
        }

        // Try firstName + lastName from comment
        if (comment.firstName || comment.lastName) {
            const firstName = comment.firstName || '';
            const lastName = comment.lastName || '';
            const fullName = `${firstName} ${lastName}`.trim();
            if (fullName) {
                return fullName;
            }
        }

        // If this comment is from the current logged-in user, use their info
        if (comment.userId === this.currentUserId) {
            if (this.currentUserName && this.currentUserName.trim()) {
                return this.currentUserName;
            }
            // Fallback to profile name if current user name not available
            if (this.userName && this.userName.trim()) {
                return this.userName;
            }
        }

        // Default fallback
        return 'User';
    }

    getCommentDisplayPhoto(comment: any): string {
        // Try comment data first
        if (comment.userProfilePhoto && comment.userProfilePhoto.trim()) {
            return comment.userProfilePhoto;
        }

        if (comment.profilePhoto && comment.profilePhoto.trim()) {
            return comment.profilePhoto;
        }

        // If this comment is from the current logged-in user, use their photo
        if (comment.userId === this.currentUserId) {
            if (this.currentUserPhoto && this.currentUserPhoto.trim()) {
                return this.currentUserPhoto;
            }
            // Fallback to profile photo if current user photo not available
            if (this.userProfilePhoto && this.userProfilePhoto.trim()) {
                return this.userProfilePhoto;
            }
        }

        // Default fallback
        return './assets/images/default-avatar.png';
    }

    getDisplayedComments(activity: any): any[] {
        if (!activity.comments) return [];

        const shouldShowAll = this.showAllComments[activity.id];
        return shouldShowAll ? activity.comments : activity.comments.slice(0, 3);
    }

    shouldShowViewAllLink(activity: any): boolean {
        return activity.comments && activity.comments.length > 3 && !this.showAllComments[activity.id];
    }

    shouldShowViewLessLink(activity: any): boolean {
        return activity.comments && activity.comments.length > 3 && this.showAllComments[activity.id];
    }

    toggleShowAllComments(activity: any): void {
        this.showAllComments[activity.id] = !this.showAllComments[activity.id];
    }

    testModal() {
        console.log('Testing modal service...');

        // Test with an existing modal type
        const modalRef = this.modalService.openModal('confirmation', {
            initialState: {
                title: 'Test Modal',
                message: 'This is a test modal to verify the modal service is working.',
                firstButtonText: 'OK',
                onConfirm: () => {
                    console.log('Test modal confirmed');
                    this.modalService.closeModal();
                }
            }
        });
        console.log('Test modal ref:', modalRef);
    }

    // Debug method to manually refresh like counts
    debugRefreshLikes() {
        console.log('=== DEBUG: Manually refreshing all like counts ===');
        console.log('Current user ID:', this.currentUserId);
        console.log('Service activities before refresh:', this.serviceActivities.map(a => ({
            id: a.id,
            likeCount: a.likeCount,
            isLiked: a.isLiked,
            likeId: a.likeId
        })));

        this.serviceActivities.forEach(activity => {
            this.refreshLikeCount(activity);
        });
    }
}